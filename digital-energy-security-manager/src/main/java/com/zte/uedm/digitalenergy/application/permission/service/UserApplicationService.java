package com.zte.uedm.digitalenergy.application.permission.service;

import com.github.pagehelper.PageInfo;
import com.zte.uedm.digitalenergy.application.permission.dto.request.CreateUserRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UpdateUserRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.request.UserQueryRequest;
import com.zte.uedm.digitalenergy.application.permission.dto.response.MenuPermissionResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.PermissionSourceResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.RoleResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.UserGroupResponse;
import com.zte.uedm.digitalenergy.application.permission.dto.response.UserResponse;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.Menu;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.Role;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.User;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.UserGroup;
import com.zte.uedm.digitalenergy.domain.permission.repository.MenuRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.RoleRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserGroupRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserRepository;
import com.zte.uedm.digitalenergy.domain.permission.service.PermissionDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户应用服务
 * 
 * <AUTHOR> Assistant
 * @since 2024-08-05
 */
@Slf4j
@Service
public class UserApplicationService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private UserGroupRepository userGroupRepository;
    
    @Autowired
    private MenuRepository menuRepository;
    
    @Autowired
    private PermissionDomainService permissionDomainService;
    
    /**
     * 创建用户
     */
    @Transactional
    public UserResponse createUser(CreateUserRequest request, String currentUser) {
        log.info("Creating user: {} by user: {}", request.getUserCode(), currentUser);
        
        // 验证中兴员工身份
        permissionDomainService.validateZteEmployee(request.getUserCode());
        
        // 验证用户工号唯一性
        permissionDomainService.validateUserCodeUnique(request.getUserCode(), null);
        
        // 验证角色分配限制
        if (request.getRoleIds() != null) {
            permissionDomainService.validateUserRoleLimit(request.getRoleIds());
            permissionDomainService.validateRoleIds(request.getRoleIds());
        }
        
        // 创建用户
        User user = User.createUser(
                request.getUsername(),
                request.getUserCode(),
                request.getOrganization(),
                currentUser
        );
        
        // 设置角色
        if (request.getRoleIds() != null && !request.getRoleIds().isEmpty()) {
            user.updateUser(currentUser, request.getRoleIds());
        }
        
        // 保存用户
        User savedUser = userRepository.save(user);
        
        log.info("Successfully created user: {}", savedUser.getUserCode());
        return convertToUserResponse(savedUser);
    }
    
    /**
     * 更新用户
     */
    @Transactional
    public UserResponse updateUser(UpdateUserRequest request, String currentUser) {
        log.info("Updating user: {} by user: {}", request.getUserId(), currentUser);
        
        // 查找用户
        User user = userRepository.findById(request.getUserId())
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + request.getUserId()));
        
        // 验证角色分配限制
        if (request.getRoleIds() != null) {
            permissionDomainService.validateUserRoleLimit(request.getRoleIds());
            permissionDomainService.validateRoleIds(request.getRoleIds());
        }
        
        // 更新用户
        user.updateUser(currentUser, request.getRoleIds());
        
        // 保存用户
        User savedUser = userRepository.save(user);
        
        log.info("Successfully updated user: {}", savedUser.getUserCode());
        return convertToUserResponse(savedUser);
    }
    
    /**
     * 删除用户
     */
    @Transactional
    public void deleteUser(Long userId) {
        log.info("Deleting user: {}", userId);
        
        // 验证用户是否存在
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + userId));
        
        // 从所有用户组中移除该用户
        List<Long> userGroupIds = userRepository.findUserGroupIdsByUserId(userId);
        for (Long userGroupId : userGroupIds) {
            UserGroup userGroup = userGroupRepository.findById(userGroupId).orElse(null);
            if (userGroup != null) {
                userGroup.removeMember(userId);
                userGroupRepository.save(userGroup);
            }
        }
        
        // 删除用户
        userRepository.deleteById(userId);
        
        log.info("Successfully deleted user: {}", user.getUserCode());
    }
    
    /**
     * 批量删除用户
     */
    @Transactional
    public void deleteUsers(List<Long> userIds) {
        log.info("Batch deleting users: {}", userIds);
        
        for (Long userId : userIds) {
            deleteUser(userId);
        }
        
        log.info("Successfully deleted {} users", userIds.size());
    }
    
    /**
     * 根据ID查询用户详情
     */
    public UserResponse getUserById(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + userId));
        
        return convertToUserResponse(user);
    }
    
    /**
     * 分页查询用户列表
     */
    public PageInfo<UserResponse> getUsersByPage(UserQueryRequest request) {
        log.info("Querying users by page: {}", request);
        
        List<User> users = userRepository.findByPage(
                request.getPageNum(),
                request.getPageSize(),
                request.getUsername(),
                request.getUserCode(),
                request.getOrganization()
        );
        
        List<UserResponse> userResponses = users.stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());
        
        return new PageInfo<>(userResponses);
    }
    
    /**
     * 获取用户权限详情
     */
    public List<PermissionSourceResponse> getUserPermissionDetails(Long userId) {
        log.info("Getting permission details for user: {}", userId);
        
        List<PermissionSourceResponse> permissionSources = new ArrayList<>();
        
        // 获取直接分配的角色权限
        List<Long> directRoleIds = userRepository.findDirectRoleIdsByUserId(userId);
        for (Long roleId : directRoleIds) {
            Role role = roleRepository.findById(roleId).orElse(null);
            if (role != null) {
                List<String> menuIds = roleRepository.findMenuIdsByRoleId(roleId);
                for (String menuId : menuIds) {
                    Menu menu = menuRepository.findById(menuId).orElse(null);
                    if (menu != null) {
                        PermissionSourceResponse source = new PermissionSourceResponse();
                        source.setMenuId(menuId);
                        source.setMenuName(menu.getMenuName());
                        source.setSourceType("DIRECT");
                        source.setSourceDescription("直接分配");
                        source.setRoleName(role.getRoleName());
                        permissionSources.add(source);
                    }
                }
            }
        }
        
        // 获取通过用户组继承的权限
        List<Long> userGroupIds = userRepository.findUserGroupIdsByUserId(userId);
        for (Long userGroupId : userGroupIds) {
            UserGroup userGroup = userGroupRepository.findById(userGroupId).orElse(null);
            if (userGroup != null) {
                List<Long> groupRoleIds = userGroupRepository.findRoleIdsByUserGroupId(userGroupId);
                for (Long roleId : groupRoleIds) {
                    Role role = roleRepository.findById(roleId).orElse(null);
                    if (role != null) {
                        List<String> menuIds = roleRepository.findMenuIdsByRoleId(roleId);
                        for (String menuId : menuIds) {
                            Menu menu = menuRepository.findById(menuId).orElse(null);
                            if (menu != null) {
                                PermissionSourceResponse source = new PermissionSourceResponse();
                                source.setMenuId(menuId);
                                source.setMenuName(menu.getMenuName());
                                source.setSourceType("INHERITED");
                                source.setSourceDescription("用户组继承");
                                source.setRoleName(role.getRoleName());
                                source.setUserGroupName(userGroup.getGroupName());
                                permissionSources.add(source);
                            }
                        }
                    }
                }
            }
        }
        
        return permissionSources;
    }

    /**
     * 获取用户的菜单权限树
     */
    public List<MenuPermissionResponse> getUserMenuPermissions(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + userId));

        // 获取所有菜单
        List<Menu> allMenus = menuRepository.findMenuTree();

        // 获取用户的权限菜单ID
        Set<String> userMenuIds = permissionDomainService.calculateUserPermissions(userId);

        // 构建菜单权限树
        return buildMenuPermissionTree(allMenus, userMenuIds);
    }

    /**
     * 转换为用户响应DTO
     */
    private UserResponse convertToUserResponse(User user) {
        UserResponse response = new UserResponse();
        response.setUserId(user.getUserId());
        response.setUsername(user.getUsername());
        response.setUserCode(user.getUserCode());
        response.setOrganization(user.getOrganization());
        response.setCreateTime(user.getCreateTime());
        response.setCreateBy(user.getCreateBy());
        response.setUpdateTime(user.getUpdateTime());
        response.setUpdateBy(user.getUpdateBy());
        
        // 设置直接分配的角色
        if (user.getRoleIds() != null) {
            List<RoleResponse> directRoles = new ArrayList<>();
            for (Long roleId : user.getRoleIds()) {
                Role role = roleRepository.findById(roleId).orElse(null);
                if (role != null) {
                    directRoles.add(convertToRoleResponse(role));
                }
            }
            response.setDirectRoles(directRoles);
        }
        
        // 设置所属用户组
        List<Long> userGroupIds = userRepository.findUserGroupIdsByUserId(user.getUserId());
        List<UserGroupResponse> userGroups = new ArrayList<>();
        for (Long userGroupId : userGroupIds) {
            UserGroup userGroup = userGroupRepository.findById(userGroupId).orElse(null);
            if (userGroup != null) {
                userGroups.add(convertToUserGroupResponse(userGroup));
            }
        }
        response.setUserGroups(userGroups);
        
        // 设置权限来源详情
        response.setPermissionSources(getUserPermissionDetails(user.getUserId()));
        
        return response;
    }
    
    /**
     * 转换为角色响应DTO（简化版）
     */
    private RoleResponse convertToRoleResponse(Role role) {
        RoleResponse response = new RoleResponse();
        response.setRoleId(role.getRoleId());
        response.setRoleName(role.getRoleName());
        response.setRoleDescription(role.getRoleDescription());
        response.setRoleType(role.getRoleType().getCode());
        response.setRoleTypeDescription(role.getRoleType().getDescription());
        return response;
    }
    
    /**
     * 转换为用户组响应DTO（简化版）
     */
    private UserGroupResponse convertToUserGroupResponse(UserGroup userGroup) {
        UserGroupResponse response = new UserGroupResponse();
        response.setUserGroupId(userGroup.getUserGroupId());
        response.setGroupName(userGroup.getGroupName());
        response.setGroupDescription(userGroup.getGroupDescription());
        return response;
    }

    /**
     * 构建菜单权限树
     */
    private List<MenuPermissionResponse> buildMenuPermissionTree(List<Menu> allMenus, Set<String> selectedMenuIds) {
        List<MenuPermissionResponse> result = new ArrayList<>();

        // 获取顶级菜单（第一层）
        List<Menu> topLevelMenus = allMenus.stream()
                .filter(menu -> menu.getMenuLevel().isTopLevel())
                .collect(Collectors.toList());

        for (Menu menu : topLevelMenus) {
            MenuPermissionResponse menuResponse = convertToMenuPermissionResponse(menu, selectedMenuIds);

            // 递归构建子菜单
            List<MenuPermissionResponse> children = buildChildMenuPermissions(menu.getMenuId(), allMenus, selectedMenuIds);
            if (!children.isEmpty()) {
                menuResponse.setChildren(children);
            }

            result.add(menuResponse);
        }

        return result;
    }

    /**
     * 递归构建子菜单权限
     */
    private List<MenuPermissionResponse> buildChildMenuPermissions(String parentMenuId, List<Menu> allMenus, Set<String> selectedMenuIds) {
        List<MenuPermissionResponse> children = new ArrayList<>();

        // 查找子菜单（菜单ID以父菜单ID开头且长度比父菜单ID多2位）
        List<Menu> childMenus = allMenus.stream()
                .filter(menu -> menu.getMenuId().startsWith(parentMenuId)
                        && menu.getMenuId().length() == parentMenuId.length() + 2
                        && !menu.getMenuId().equals(parentMenuId))
                .collect(Collectors.toList());

        for (Menu childMenu : childMenus) {
            MenuPermissionResponse childResponse = convertToMenuPermissionResponse(childMenu, selectedMenuIds);

            // 递归处理子菜单
            List<MenuPermissionResponse> grandChildren = buildChildMenuPermissions(childMenu.getMenuId(), allMenus, selectedMenuIds);
            if (!grandChildren.isEmpty()) {
                childResponse.setChildren(grandChildren);
            }

            children.add(childResponse);
        }

        return children;
    }

    /**
     * 转换为菜单权限响应DTO
     */
    private MenuPermissionResponse convertToMenuPermissionResponse(Menu menu, Set<String> selectedMenuIds) {
        MenuPermissionResponse response = new MenuPermissionResponse();
        response.setMenuId(menu.getMenuId());
        response.setMenuName(menu.getMenuName());
        response.setMenuPath(menu.getMenuPath());
        response.setMenuLevel(menu.getMenuLevel().getLevel());
        response.setSortOrder(menu.getSortOrder());
        response.setSelected(selectedMenuIds != null && selectedMenuIds.contains(menu.getMenuId()));
        response.setHasPermission(selectedMenuIds != null && selectedMenuIds.contains(menu.getMenuId()));
        return response;
    }
}
